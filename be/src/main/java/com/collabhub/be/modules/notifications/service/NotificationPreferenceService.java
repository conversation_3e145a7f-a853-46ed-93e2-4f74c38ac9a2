package com.collabhub.be.modules.notifications.service;

import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.auth.dto.UserType;
import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.notifications.constants.NotificationConstants;
import com.collabhub.be.modules.notifications.converter.NotificationPreferenceConverter;
import com.collabhub.be.modules.notifications.dto.*;
import com.collabhub.be.modules.notifications.repository.NotificationPreferenceRepository;
import org.jooq.generated.tables.pojos.NotificationPreference;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;

/**
 * Service for managing user notification preferences.
 * Handles CRUD operations for notification preferences with proper validation.
 */
@Service
@Validated
public class NotificationPreferenceService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationPreferenceService.class);

    private final NotificationPreferenceRepository repository;
    private final NotificationPreferenceConverter converter;
    private final JwtClaimsService jwtClaimsService;

    public NotificationPreferenceService(NotificationPreferenceRepository repository,
                                       NotificationPreferenceConverter converter,
                                       JwtClaimsService jwtClaimsService) {
        this.repository = repository;
        this.converter = converter;
        this.jwtClaimsService = jwtClaimsService;
    }

    /**
     * Retrieves all notification preferences for the current user.
     * Creates default preferences if none exist.
     * Handles both internal users and external participants.
     *
     * @return list of notification preferences
     */
    @Transactional
    public List<NotificationPreferenceResponse> getUserNotificationPreferences() {
        UserContext userContext = jwtClaimsService.getCurrentUser();

        List<NotificationPreference> preferences;

        if (userContext.getUserType() == UserType.INTERNAL) {
            // Internal user - use user ID
            Long userId = userContext.getUserId();
            logger.debug("Retrieving notification preferences for internal user: {}", userId);
            preferences = repository.findByUserId(userId);

            if (preferences.isEmpty()) {
                preferences = createDefaultPreferencesForUser(userId, userContext.getEmail(), userContext.getUserType());
            }
        } else {
            // External participant - use email only (global preferences)
            String email = userContext.getEmail();
            logger.debug("Retrieving notification preferences for external participant: {}", email);
            preferences = repository.findByEmail(email);

            if (preferences.isEmpty()) {
                preferences = createDefaultPreferencesForEmail(email, userContext.getUserType());
            }
        }

        logger.info(NotificationConstants.PREFERENCES_RETRIEVED_LOG, preferences.size(),
                   userContext.getUserType() == UserType.INTERNAL ? userContext.getUserId().toString() : userContext.getEmail());
        return converter.toResponseList(preferences);
    }

    /**
     * Updates notification preferences for the current user.
     * Handles both internal users and external participants.
     *
     * @param request the update request containing preference changes
     * @return updated list of notification preferences
     */
    @Transactional
    public List<NotificationPreferenceResponse> updateNotificationPreferences(@Valid @NotNull NotificationPreferenceUpdateRequest request) {
        UserContext userContext = jwtClaimsService.getCurrentUser();

        validateUpdateRequest(request, userContext.getUserType());

        List<NotificationPreference> updatedPreferences = new ArrayList<>();

        if (userContext.getUserType() == UserType.INTERNAL) {
            // Internal user - use user ID
            Long userId = userContext.getUserId();
            logger.debug("Updating notification preferences for internal user: {}", userId);

            for (NotificationPreferenceUpdateRequest.PreferenceUpdate update : request.getPreferences()) {
                NotificationPreference preference = repository.upsertPreferenceForUser(
                        userId, userContext.getEmail(), update.getType(), update.getChannel(), update.getEnabled());
                updatedPreferences.add(preference);

                logPreferenceUpdateForUser(userId, update);
            }
        } else {
            // External participant - use email only (global preferences)
            String email = userContext.getEmail();
            logger.debug("Updating notification preferences for external participant: {}", email);

            for (NotificationPreferenceUpdateRequest.PreferenceUpdate update : request.getPreferences()) {
                NotificationPreference preference = repository.upsertPreferenceForEmail(
                        email, update.getType(), update.getChannel(), update.getEnabled());
                updatedPreferences.add(preference);

                logPreferenceUpdateForEmail(email, update);
            }
        }

        logger.info(NotificationConstants.PREFERENCES_UPDATED_LOG, updatedPreferences.size(),
                   userContext.getUserType() == UserType.INTERNAL ? userContext.getUserId().toString() : userContext.getEmail());
        return converter.toResponseList(updatedPreferences);
    }

    /**
     * Creates default notification preferences for an internal user.
     */
    private List<NotificationPreference> createDefaultPreferencesForUser(Long userId, String email, UserType userType) {
        logger.debug("Creating default notification preferences for internal user: {} ({})", userId, email);

        List<NotificationPreference> preferences = new ArrayList<>();

        for (NotificationType type : NotificationType.values()) {
            // All users get EMAIL preferences
            preferences.add(repository.upsertPreferenceForUser(userId, email, type, NotificationChannel.EMAIL,
                    NotificationConstants.DEFAULT_NOTIFICATION_ENABLED));

            // Only internal users get IN_APP preferences
            if (userType == UserType.INTERNAL) {
                preferences.add(repository.upsertPreferenceForUser(userId, email, type, NotificationChannel.IN_APP,
                        NotificationConstants.DEFAULT_NOTIFICATION_ENABLED));
            }
        }

        return preferences;
    }

    /**
     * Creates default notification preferences for an external participant.
     * External participants have global email-only preferences.
     */
    private List<NotificationPreference> createDefaultPreferencesForEmail(String email, UserType userType) {
        logger.debug("Creating default notification preferences for external participant: {}", email);

        List<NotificationPreference> preferences = new ArrayList<>();

        for (NotificationType type : NotificationType.values()) {
            // External participants only get EMAIL preferences (global across accounts)
            preferences.add(repository.upsertPreferenceForEmail(email, type, NotificationChannel.EMAIL,
                    NotificationConstants.DEFAULT_NOTIFICATION_ENABLED));
        }

        return preferences;
    }

    /**
     * Validates the update request based on user type.
     */
    private void validateUpdateRequest(NotificationPreferenceUpdateRequest request, UserType userType) {
        if (request.getPreferences().size() > NotificationConstants.MAX_PREFERENCES_PER_REQUEST) {
            throw new BadRequestException(ErrorCode.VALIDATION_FAILED, 
                    "Too many preferences in single request");
        }

        // External users cannot use IN_APP channel
        if (userType != UserType.INTERNAL) {
            boolean hasInAppChannel = request.getPreferences().stream()
                    .anyMatch(pref -> pref.getChannel() == NotificationChannel.IN_APP);
            
            if (hasInAppChannel) {
                throw new BadRequestException(ErrorCode.VALIDATION_FAILED, 
                        NotificationConstants.INVALID_CHANNEL_FOR_EXTERNAL_USER);
            }
        }
    }



    /**
     * Logs preference update for internal user audit trail.
     */
    private void logPreferenceUpdateForUser(Long userId, NotificationPreferenceUpdateRequest.PreferenceUpdate update) {
        logger.info(NotificationConstants.PREFERENCE_UPDATED_LOG,
                   userId, update.getType(), update.getChannel(), update.getEnabled());
    }

    /**
     * Logs preference update for external participant audit trail.
     */
    private void logPreferenceUpdateForEmail(String email, NotificationPreferenceUpdateRequest.PreferenceUpdate update) {
        logger.info("Notification preference updated for external participant - email: {}, type: {}, channel: {}, enabled: {}",
                   email, update.getType(), update.getChannel(), update.getEnabled());
    }

    /**
     * Checks if an email notification should be sent based on user preferences.
     * Always sends critical notifications like invitations regardless of preferences.
     *
     * @param email the email address to check
     * @param type the notification type
     * @return true if the email should be sent, false otherwise
     */
    public boolean shouldSendEmailNotification(String email, NotificationType type) {
        // Check user preferences for email notifications
        Boolean enabled = repository.isNotificationEnabledForEmail(email, type, NotificationChannel.EMAIL);
        return enabled == null || enabled; // Default to enabled if no preference exists
    }

    // ========================================
    // UNIFIED EXTERNAL USER METHODS
    // ========================================

    /**
     * Checks if a notification is enabled for an external user by email and channel.
     *
     * <p>This method provides feature parity with internal user preference checking,
     * using email-based identification for external users (hub participants).</p>
     *
     * @param email the email address of the external user (must be valid)
     * @param type the notification type (must not be null)
     * @param channel the notification channel (must not be null)
     * @return true if enabled, false if disabled, null if no preference set (defaults to enabled)
     *
     * @throws IllegalArgumentException if parameters are invalid
     */
    @Transactional(readOnly = true)
    public Boolean isExternalNotificationEnabled(@NotBlank String email,
                                                @NotNull NotificationType type,
                                                @NotNull NotificationChannel channel) {

        validateExternalPreferenceParameters(email, type, channel);
        String normalizedEmail = email.trim().toLowerCase();

        return repository.isNotificationEnabledForEmail(normalizedEmail, type, channel);
    }

    /**
     * Validates external user preference parameters.
     */
    private void validateExternalPreferenceParameters(String email, NotificationType type, NotificationChannel channel) {
        validateEmailParameter(email);

        if (type == null) {
            throw new IllegalArgumentException("Notification type cannot be null");
        }

        if (channel == null) {
            throw new IllegalArgumentException("Notification channel cannot be null");
        }

        validateExternalChannelSupport(channel);
    }

    /**
     * Validates email parameter format.
     */
    private void validateEmailParameter(String email) {
        if (email == null || email.trim().isEmpty()) {
            throw new IllegalArgumentException("Email cannot be null or empty");
        }

        if (!email.contains("@") || !email.contains(".")) {
            throw new IllegalArgumentException("Invalid email format: " + email);
        }

        if (email.length() > 255) {
            throw new IllegalArgumentException("Email exceeds maximum length of 255 characters");
        }
    }

    /**
     * Validates that external users can only use supported channels.
     */
    private void validateExternalChannelSupport(NotificationChannel channel) {
        if (channel == NotificationChannel.IN_APP) {
            throw new IllegalArgumentException("External users cannot use IN_APP channel - only EMAIL is supported");
        }
    }
}
