package com.collabhub.be.modules.notifications.controller;

import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.notifications.dto.NotificationPageRequest;
import com.collabhub.be.modules.notifications.dto.NotificationPageResponse;
import com.collabhub.be.modules.notifications.service.ExternalNotificationManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Production-grade REST controller for external user notification operations.
 *
 * <p>This controller provides endpoints for external users (hub participants without user accounts)
 * to manage their notifications. It maintains the same API patterns as internal user notifications
 * while ensuring proper security isolation and email-based identification.</p>
 *
 * <h3>Key Features:</h3>
 * <ul>
 *   <li>Email-based notification access with security validation</li>
 *   <li>0-based pagination with validation</li>
 *   <li>Proper HTTP status codes and error handling</li>
 *   <li>Comprehensive API documentation with OpenAPI annotations</li>
 *   <li>Minimal controller logic (10-15 lines per endpoint)</li>
 * </ul>
 *
 * <h3>Security:</h3>
 * <ul>
 *   <li>Requires EXTERNAL_PARTICIPANT role</li>
 *   <li>Email validation against authenticated user context</li>
 *   <li>Hub-scoped access restrictions</li>
 * </ul>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/external/notifications")
@Tag(name = "External Notifications", description = "Notification management for external users (hub participants)")
public class ExternalNotificationController {

    private static final Logger logger = LoggerFactory.getLogger(ExternalNotificationController.class);

    private final ExternalNotificationManagementService externalNotificationManagementService;
    private final JwtClaimsService jwtClaimsService;

    public ExternalNotificationController(ExternalNotificationManagementService externalNotificationManagementService,
                                        JwtClaimsService jwtClaimsService) {
        this.externalNotificationManagementService = externalNotificationManagementService;
        this.jwtClaimsService = jwtClaimsService;
    }

    /**
     * Gets notifications for an external user by email.
     *
     * <p>This endpoint provides paginated access to notifications for external users
     * identified by email address. It validates that the email matches the authenticated
     * external user's context to prevent unauthorized access.</p>
     *
     * @param email the email address of the external user (must match authenticated user)
     * @param page the page number (0-based, min: 0, max: 1000)
     * @param size the page size (min: 1, max: 100)
     * @param unreadOnly whether to return only unread notifications
     * @return paginated notification response
     */
    @GetMapping
    @PreAuthorize("hasRole('EXTERNAL_PARTICIPANT')")
    @Operation(
        summary = "Get external user notifications", 
        description = "Retrieves paginated notifications for an authenticated external user with email-based identification"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Notifications retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid pagination parameters or email format"),
            @ApiResponse(responseCode = "401", description = "Authentication required - missing or invalid JWT token"),
            @ApiResponse(responseCode = "403", description = "Access denied - email does not match authenticated user or insufficient permissions"),
            @ApiResponse(responseCode = "500", description = "Internal server error during notification retrieval")
    })
    public ResponseEntity<NotificationPageResponse> getExternalNotifications(
            @Parameter(description = "Email address of the external user", example = "<EMAIL>", required = true)
            @RequestParam String email,
            @Parameter(description = "Page number (0-based, min: 0, max: 1000)", example = "0") 
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size (min: 1, max: 100)", example = "20") 
            @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Return only unread notifications", example = "false") 
            @RequestParam(defaultValue = "false") boolean unreadOnly) {

        validateExternalUserEmail(email);
        NotificationPageRequest pageRequest = NotificationPageRequest.of(page, size, unreadOnly);
        NotificationPageResponse notifications = externalNotificationManagementService.getExternalUserNotifications(email, pageRequest);
        return ResponseEntity.ok(notifications);
    }

    /**
     * Marks all notifications as read for an external user.
     *
     * <p>This endpoint performs a bulk update operation to mark all unread notifications
     * as read for the authenticated external user. It returns the number of notifications
     * that were actually updated.</p>
     *
     * @param email the email address of the external user (must match authenticated user)
     * @return number of notifications marked as read
     */
    @PostMapping("/mark-all-read")
    @PreAuthorize("hasRole('EXTERNAL_PARTICIPANT')")
    @Operation(
        summary = "Mark all notifications as read for external user", 
        description = "Marks all unread notifications as read for the authenticated external user. Returns the count of updated notifications."
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "All notifications marked as read successfully - returns count of updated notifications"),
            @ApiResponse(responseCode = "400", description = "Invalid email format"),
            @ApiResponse(responseCode = "401", description = "Authentication required - missing or invalid JWT token"),
            @ApiResponse(responseCode = "403", description = "Access denied - email does not match authenticated user or insufficient permissions"),
            @ApiResponse(responseCode = "500", description = "Internal server error during bulk notification update")
    })
    public ResponseEntity<Integer> markAllNotificationsAsRead(
            @Parameter(description = "Email address of the external user", example = "<EMAIL>", required = true)
            @RequestParam String email) {

        validateExternalUserEmail(email);
        int updatedCount = externalNotificationManagementService.markAllNotificationsAsRead(email);
        return ResponseEntity.ok(updatedCount);
    }

    /**
     * Gets the count of unread notifications for an external user.
     *
     * <p>This endpoint provides a fast count of unread notifications for the authenticated
     * external user, commonly used for notification badges in the UI.</p>
     *
     * @param email the email address of the external user (must match authenticated user)
     * @return number of unread notifications
     */
    @GetMapping("/unread-count")
    @PreAuthorize("hasRole('EXTERNAL_PARTICIPANT')")
    @Operation(
        summary = "Get unread notification count for external user", 
        description = "Retrieves the total count of unread notifications for the authenticated external user. Used for notification badges."
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Unread count retrieved successfully - returns non-negative integer"),
            @ApiResponse(responseCode = "400", description = "Invalid email format"),
            @ApiResponse(responseCode = "401", description = "Authentication required - missing or invalid JWT token"),
            @ApiResponse(responseCode = "403", description = "Access denied - email does not match authenticated user or insufficient permissions"),
            @ApiResponse(responseCode = "500", description = "Internal server error during count retrieval")
    })
    public ResponseEntity<Integer> getUnreadNotificationCount(
            @Parameter(description = "Email address of the external user", example = "<EMAIL>", required = true)
            @RequestParam String email) {

        validateExternalUserEmail(email);
        int count = externalNotificationManagementService.getUnreadNotificationCount(email);
        return ResponseEntity.ok(count);
    }

    /**
     * Validates that the provided email matches the authenticated external user.
     *
     * <p>This method ensures that external users can only access their own notifications
     * by validating the email parameter against the authenticated user's context.</p>
     *
     * @param email the email to validate
     * @throws IllegalArgumentException if email doesn't match authenticated user
     */
    private void validateExternalUserEmail(String email) {
        // Get the authenticated external user's email from JWT claims
        String authenticatedEmail = jwtClaimsService.getCurrentUser().getEmail();
        
        if (authenticatedEmail == null || !authenticatedEmail.equalsIgnoreCase(email.trim())) {
            throw new IllegalArgumentException("Email parameter must match authenticated external user");
        }
    }
}
