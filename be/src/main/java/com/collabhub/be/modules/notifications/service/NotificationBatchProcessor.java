package com.collabhub.be.modules.notifications.service;

import com.collabhub.be.modules.notifications.config.NotificationBatchingProperties;
import com.collabhub.be.modules.notifications.repository.NotificationBatchProcessingLockRepository;
import com.collabhub.be.modules.notifications.repository.NotificationBatchQueueRepository;
import org.jooq.generated.tables.pojos.NotificationBatchQueue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.InetAddress;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Scheduled processor for handling batched email notifications.
 * Runs periodically to process accumulated notifications and send consolidated emails.
 */
@Service
public class NotificationBatchProcessor {

    private static final Logger logger = LoggerFactory.getLogger(NotificationBatchProcessor.class);
    
    private static final String BATCH_PROCESSOR_LOCK_KEY = "notification_batch_processor";
    private static final int CLEANUP_BATCH_SIZE = 1000;

    private final NotificationBatchingProperties batchingProperties;
    private final NotificationBatchQueueRepository batchQueueRepository;
    private final NotificationBatchProcessingLockRepository lockRepository;
    private final BatchedEmailNotificationService batchedEmailService;
    
    private final String instanceId;

    @Autowired
    private ApplicationContext applicationContext;

    public NotificationBatchProcessor(NotificationBatchingProperties batchingProperties,
                                    NotificationBatchQueueRepository batchQueueRepository,
                                    NotificationBatchProcessingLockRepository lockRepository,
                                    BatchedEmailNotificationService batchedEmailService) {
        this.batchingProperties = batchingProperties;
        this.batchQueueRepository = batchQueueRepository;
        this.lockRepository = lockRepository;
        this.batchedEmailService = batchedEmailService;
        this.instanceId = generateInstanceId();
    }

    /**
     * Main scheduled method that processes batched notifications.
     * Runs at the configured interval to send accumulated notifications.
     */
    @Scheduled(fixedRateString = "#{@notificationBatchingProperties.processIntervalSeconds * 1000}")
    public void processBatchedNotifications() {
        if (!batchingProperties.isEnabled()) {
            logger.debug("Notification batching is disabled, skipping batch processing");
            return;
        }

        logger.debug("Starting batch notification processing");

        // Try to acquire distributed lock
        boolean lockAcquired = lockRepository.acquireLock(
            BATCH_PROCESSOR_LOCK_KEY, 
            instanceId, 
            batchingProperties.getLockTimeoutMinutes()
        );

        if (!lockAcquired) {
            logger.debug("Could not acquire batch processing lock, another instance is processing");
            return;
        }

        try {
            // Use proxy to ensure @Transactional annotations work properly
            NotificationBatchProcessor self = applicationContext.getBean(NotificationBatchProcessor.class);
            self.processReadyBatches();
            self.processRetryableNotifications();
        } catch (Exception e) {
            logger.error("Error during batch notification processing", e);
        } finally {
            // Always release the lock
            lockRepository.releaseLock(BATCH_PROCESSOR_LOCK_KEY, instanceId);
        }
    }

    /**
     * Processes batches that are ready for delivery.
     */
    @Transactional
    protected void processReadyBatches() {
        LocalDateTime cutoffTime = LocalDateTime.now().minusMinutes(1); // 1-minute buffer
        
        Map<String, List<NotificationBatchQueue>> readyBatches =
            batchQueueRepository.findPendingBatches(cutoffTime, batchingProperties.getMaxBatchSize());

        if (readyBatches.isEmpty()) {
            logger.debug("No ready batches found for processing");
            return;
        }

        logger.info("Processing {} ready batches", readyBatches.size());

        for (Map.Entry<String, List<NotificationBatchQueue>> entry : readyBatches.entrySet()) {
            String batchKey = entry.getKey();
            List<NotificationBatchQueue> notifications = entry.getValue();
            
            try {
                processSingleBatch(batchKey, notifications);
            } catch (Exception e) {
                logger.error("Failed to process batch: {}", batchKey, e);
                handleBatchProcessingFailure(notifications, e.getMessage());
            }
        }
    }

    /**
     * Processes a single batch of notifications for a user.
     */
    private void processSingleBatch(String batchKey, List<NotificationBatchQueue> notifications) {
        if (notifications.isEmpty()) {
            return;
        }

        // Extract user ID from batch key (format: userId_timestamp)
        Long userId = Long.parseLong(batchKey.split("_")[0]);
        
        logger.debug("Processing batch for user {} with {} notifications", userId, notifications.size());

        // Mark notifications as processing
        List<Long> notificationIds = notifications.stream()
                .map(NotificationBatchQueue::getId)
                .collect(Collectors.toList());
        
        int markedCount = batchQueueRepository.markAsProcessing(notificationIds);
        if (markedCount != notifications.size()) {
            logger.warn("Expected to mark {} notifications as processing, but marked {}", 
                       notifications.size(), markedCount);
        }

        try {
            // Send batched email
            boolean emailSent = batchedEmailService.sendBatchedEmail(userId, notifications);
            
            if (emailSent) {
                // Mark as successfully sent
                batchQueueRepository.markAsSent(notificationIds);
                logger.info("Successfully sent batched email to user {} with {} notifications", 
                           userId, notifications.size());
            } else {
                // Mark as failed for retry
                batchQueueRepository.markAsFailed(notificationIds, "Email delivery failed");
                logger.warn("Failed to send batched email to user {}", userId);
            }
            
        } catch (Exception e) {
            // Mark as failed for retry
            batchQueueRepository.markAsFailed(notificationIds, e.getMessage());
            logger.error("Exception while sending batched email to user {}", userId, e);
        }
    }

    /**
     * Processes notifications that are ready for retry.
     */
    @Transactional
    protected void processRetryableNotifications() {
        LocalDateTime retryAfter = LocalDateTime.now().minusMinutes(batchingProperties.getRetryDelayMinutes());
        
        List<NotificationBatchQueue> retryableNotifications =
            batchQueueRepository.findRetryableNotifications(retryAfter, batchingProperties.getMaxRetryAttempts());

        if (retryableNotifications.isEmpty()) {
            logger.debug("No notifications ready for retry");
            return;
        }

        logger.info("Processing {} notifications for retry", retryableNotifications.size());

        // Group by user for batch processing
        Map<Long, List<NotificationBatchQueue>> notificationsByUser = retryableNotifications.stream()
                .collect(Collectors.groupingBy(NotificationBatchQueue::getUserId));

        for (Map.Entry<Long, List<NotificationBatchQueue>> entry : notificationsByUser.entrySet()) {
            Long userId = entry.getKey();
            List<NotificationBatchQueue> userNotifications = entry.getValue();
            
            try {
                // Reset to pending and let normal processing handle them
                List<Long> notificationIds = userNotifications.stream()
                        .map(NotificationBatchQueue::getId)
                        .collect(Collectors.toList());
                
                batchQueueRepository.resetToPending(notificationIds);
                logger.debug("Reset {} notifications to pending for user {}", userNotifications.size(), userId);
                
            } catch (Exception e) {
                logger.error("Failed to reset notifications for retry for user {}", userId, e);
            }
        }
    }

    /**
     * Handles batch processing failures by marking notifications as failed.
     */
    private void handleBatchProcessingFailure(List<NotificationBatchQueue> notifications, String errorMessage) {
        List<Long> notificationIds = notifications.stream()
                .map(NotificationBatchQueue::getId)
                .collect(Collectors.toList());
        
        batchQueueRepository.markAsFailed(notificationIds, errorMessage);
    }

    /**
     * Scheduled cleanup of old processed notifications.
     */
    @Scheduled(cron = "0 0 2 * * *") // Daily at 2 AM
    public void cleanupProcessedNotifications() {
        if (!batchingProperties.isEnabled()) {
            return;
        }

        logger.debug("Starting cleanup of processed notifications");

        LocalDateTime cutoffTime = LocalDateTime.now().minusHours(batchingProperties.getCleanupRetentionHours());
        int deletedCount = batchQueueRepository.cleanupProcessedNotifications(cutoffTime, CLEANUP_BATCH_SIZE);
        
        logger.info("Cleaned up {} processed notifications", deletedCount);

        // Also cleanup expired locks
        int expiredLocks = lockRepository.cleanupExpiredLocks();
        if (expiredLocks > 0) {
            logger.info("Cleaned up {} expired processing locks", expiredLocks);
        }
    }

    /**
     * Processes any pending batches on application startup.
     */
    @EventListener(ApplicationReadyEvent.class)
    public void processPendingBatchesOnStartup() {
        if (!batchingProperties.isEnabled()) {
            return;
        }

        logger.info("Processing pending batches on application startup");
        
        try {
            processBatchedNotifications();
        } catch (Exception e) {
            logger.error("Error processing pending batches on startup", e);
        }
    }

    /**
     * Generates a unique instance identifier for distributed locking.
     */
    private String generateInstanceId() {
        try {
            String hostname = InetAddress.getLocalHost().getHostName();
            return hostname + "-" + System.currentTimeMillis();
        } catch (Exception e) {
            return "unknown-" + System.currentTimeMillis();
        }
    }
}
