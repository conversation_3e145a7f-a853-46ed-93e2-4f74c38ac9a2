package com.collabhub.be.modules.notifications.controller;

import com.collabhub.be.modules.notifications.dto.MarkNotificationReadRequest;
import com.collabhub.be.modules.notifications.dto.NotificationPageRequest;
import com.collabhub.be.modules.notifications.dto.NotificationPageResponse;
import com.collabhub.be.modules.notifications.dto.NotificationResponse;
import com.collabhub.be.modules.notifications.service.NotificationManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * Production-grade REST controller for in-app notification operations.
 *
 * <p>This controller provides endpoints for notification management with minimal logic per endpoint,
 * comprehensive error handling, and proper HTTP status codes. Each endpoint delegates business logic
 * to the service layer while maintaining clear separation of concerns.</p>
 *
 * <h3>Key Features:</h3>
 * <ul>
 *   <li>0-based pagination with validation</li>
 *   <li>Proper HTTP status codes (401 for auth, 403 for authorization, 404 for not found)</li>
 *   <li>Comprehensive API documentation with OpenAPI annotations</li>
 *   <li>Minimal controller logic (10-15 lines per endpoint)</li>
 *   <li>Consistent error responses and logging</li>
 * </ul>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/notifications")
@Tag(name = "Notifications", description = "Production-grade in-app notification management with comprehensive validation")
public class NotificationController {

    private static final Logger logger = LoggerFactory.getLogger(NotificationController.class);

    private final NotificationManagementService notificationManagementService;

    public NotificationController(NotificationManagementService notificationManagementService) {
        this.notificationManagementService = notificationManagementService;
    }

    /**
     * Retrieves notifications for the current user with pagination.
     *
     * @param page the page number (0-based)
     * @param size the page size
     * @param unreadOnly whether to return only unread notifications
     * @return page of notifications
     */
    @GetMapping
    @PreAuthorize("isAuthenticated()")
    @Operation(
        summary = "Get user notifications",
        description = "Retrieves paginated notifications for the authenticated user with optional filtering for unread notifications only"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Notifications retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid pagination parameters"),
            @ApiResponse(responseCode = "401", description = "Authentication required - missing or invalid JWT token"),
            @ApiResponse(responseCode = "500", description = "Internal server error during notification retrieval")
    })
    public ResponseEntity<NotificationPageResponse> getNotifications(
            @Parameter(description = "Page number (0-based, min: 0, max: 1000)", example = "0")
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size (min: 1, max: 100)", example = "20")
            @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Return only unread notifications", example = "false")
            @RequestParam(defaultValue = "false") boolean unreadOnly) {

        NotificationPageRequest pageRequest = NotificationPageRequest.of(page, size, unreadOnly);
        NotificationPageResponse notifications = notificationManagementService.getUserNotifications(pageRequest);
        return ResponseEntity.ok(notifications);
    }

    /**
     * Marks a specific notification as read.
     *
     * @param request the mark as read request
     * @return success response
     */
    @PostMapping("/mark-read")
    @PreAuthorize("isAuthenticated()")
    @Operation(
        summary = "Mark notification as read",
        description = "Marks a specific notification as read for the authenticated user. Only the notification owner can mark it as read."
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Notification marked as read successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data - notification ID must be positive"),
            @ApiResponse(responseCode = "401", description = "Authentication required - missing or invalid JWT token"),
            @ApiResponse(responseCode = "404", description = "Notification not found or not owned by the authenticated user"),
            @ApiResponse(responseCode = "500", description = "Internal server error during notification update")
    })
    public ResponseEntity<Void> markNotificationAsRead(@Valid @RequestBody MarkNotificationReadRequest request) {
        notificationManagementService.markNotificationAsRead(request.getNotificationId());
        return ResponseEntity.ok().build();
    }

    /**
     * Marks all notifications as read for the current user.
     *
     * @return number of notifications marked as read
     */
    @PostMapping("/mark-all-read")
    @PreAuthorize("isAuthenticated()")
    @Operation(
        summary = "Mark all notifications as read",
        description = "Marks all unread notifications as read for the authenticated user. Returns the number of notifications that were actually updated."
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "All notifications marked as read successfully - returns count of updated notifications"),
            @ApiResponse(responseCode = "401", description = "Authentication required - missing or invalid JWT token"),
            @ApiResponse(responseCode = "500", description = "Internal server error during bulk notification update")
    })
    public ResponseEntity<Integer> markAllNotificationsAsRead() {
        int updatedCount = notificationManagementService.markAllNotificationsAsRead();
        return ResponseEntity.ok(updatedCount);
    }

    /**
     * Gets the count of unread notifications for the current user.
     *
     * @return number of unread notifications
     */
    @GetMapping("/unread-count")
    @PreAuthorize("isAuthenticated()")
    @Operation(
        summary = "Get unread notification count",
        description = "Retrieves the total count of unread notifications for the authenticated user. Commonly used for notification badges in the UI."
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Unread count retrieved successfully - returns non-negative integer"),
            @ApiResponse(responseCode = "401", description = "Authentication required - missing or invalid JWT token"),
            @ApiResponse(responseCode = "500", description = "Internal server error during count retrieval")
    })
    public ResponseEntity<Integer> getUnreadNotificationCount() {
        int count = notificationManagementService.getUnreadNotificationCount();
        return ResponseEntity.ok(count);
    }
}
