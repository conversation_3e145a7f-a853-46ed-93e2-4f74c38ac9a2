package com.collabhub.be.modules.notifications.service;

import com.collabhub.be.modules.auth.dto.UserType;
import com.collabhub.be.modules.auth.repository.UserRepository;
import com.collabhub.be.modules.notifications.constants.NotificationConstants;
import com.collabhub.be.modules.notifications.dto.ExternalUserRecipient;
import com.collabhub.be.modules.notifications.dto.InternalUserRecipient;
import com.collabhub.be.modules.notifications.dto.NotificationChannel;
import com.collabhub.be.modules.notifications.dto.NotificationMetadata;
import com.collabhub.be.modules.notifications.dto.NotificationRecipient;
import com.collabhub.be.modules.notifications.dto.NotificationType;
import com.collabhub.be.modules.notifications.dto.NotificationUrgency;
import com.collabhub.be.modules.notifications.exception.NotificationBatchingException;
import com.collabhub.be.modules.notifications.util.NotificationRecipientUtils;
import org.jooq.generated.tables.pojos.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Production-grade notification dispatcher service that coordinates notification delivery.
 *
 * <p>This service provides the main entry point for dispatching notifications throughout
 * the application, handling routing between in-app and email channels based on user types,
 * preferences, and notification urgency levels. It uses strongly-typed data structures
 * and comprehensive error handling for production reliability.</p>
 *
 * <h3>Key Features:</h3>
 * <ul>
 *   <li>Strongly-typed metadata using {@link NotificationMetadata} for type safety</li>
 *   <li>Urgency-based routing with {@link NotificationUrgency} for batching control</li>
 *   <li>Automatic user type detection and channel routing</li>
 *   <li>Comprehensive logging and error handling</li>
 *   <li>Production-grade validation and null safety</li>
 * </ul>
 *
 * <h3>Usage Examples:</h3>
 * <pre>
 * // Dispatch with strongly-typed metadata
 * NotificationMetadata metadata = NotificationMetadata.builder()
 *     .actorName("John Doe")
 *     .targetTitle("Summer Campaign")
 *     .actionContext("mentioned you in a comment")
 *     .deepLinkPath("/app/posts/123#comment-456")
 *     .build();
 *
 * dispatcher.dispatchNotification(
 *     NotificationType.COMMENT_MENTION,
 *     "You were mentioned",
 *     "John Doe mentioned you in a comment",
 *     List.of(userId),
 *     entityReferences,
 *     metadata,
 *     NotificationUrgency.HIGH
 * );
 * </pre>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
@Service
@Validated
public class NotificationDispatcherService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationDispatcherService.class);

    // Constants
    private static final int MAX_TITLE_LENGTH = 255;
    private static final int MAX_MESSAGE_LENGTH = 1000;
    private static final String EMPTY_RECIPIENTS_MESSAGE = "No recipients for notification type: {}";
    private static final String DISPATCH_START_MESSAGE = "Dispatching notification: type={}, recipients={}";
    private static final String DISPATCH_COMPLETE_MESSAGE = "Notification dispatch completed: type={}, urgency={}, inApp={}, email={}";
    private static final String NO_VALID_EMAIL_RECIPIENTS_MESSAGE = "No valid email recipients found for notification type: {}";
    private static final String MIXED_DISPATCH_START_MESSAGE = "Dispatching mixed notification: type={}, internal={}, external={}";
    private static final String MIXED_DISPATCH_COMPLETE_MESSAGE = "Mixed notification dispatch completed: type={}, urgency={}, internal={}, external={}";

    private final NotificationService notificationService;
    private final NotificationStorageService notificationStorageService;
    private final EmailNotificationService emailNotificationService;
    private final NotificationBatchingService notificationBatchingService;
    private final UserRepository userRepository;
    private final NotificationPreferenceService notificationPreferenceService;
    private final ExternalEmailNotificationService externalEmailNotificationService;

    public NotificationDispatcherService(NotificationService notificationService,
                                       NotificationStorageService notificationStorageService,
                                       EmailNotificationService emailNotificationService,
                                       NotificationBatchingService notificationBatchingService,
                                       UserRepository userRepository,
                                       NotificationPreferenceService notificationPreferenceService,
                                       ExternalEmailNotificationService externalEmailNotificationService) {
        this.notificationService = notificationService;
        this.notificationStorageService = notificationStorageService;
        this.emailNotificationService = emailNotificationService;
        this.notificationBatchingService = notificationBatchingService;
        this.userRepository = userRepository;
        this.notificationPreferenceService = notificationPreferenceService;
        this.externalEmailNotificationService = externalEmailNotificationService;
    }



    /**
     * Dispatches a notification to multiple users through appropriate channels.
     *
     * <p>This is the preferred method for dispatching notifications, using strongly-typed
     * metadata and automatic urgency detection based on notification type.</p>
     *
     * @param type the notification type (must not be null)
     * @param title the notification title (max 255 characters)
     * @param message the notification message (max 1000 characters)
     * @param recipientUserIds the user IDs to notify (must not be empty)
     * @param entityReferences optional entity references for deep linking
     * @param metadata optional strongly-typed metadata providing additional context
     *
     * @throws IllegalArgumentException if required parameters are invalid
     * @throws NotificationBatchingException if dispatch processing fails
     */
    @Transactional
    public void dispatchNotification(@NotNull @Valid NotificationType type,
                                   @NotBlank @Size(max = MAX_TITLE_LENGTH) String title,
                                   @NotBlank @Size(max = MAX_MESSAGE_LENGTH) String message,
                                   @NotEmpty List<Long> recipientUserIds,
                                   NotificationStorageService.EntityReferences entityReferences,
                                   @Valid NotificationMetadata metadata) {

        NotificationUrgency urgency = determineDefaultUrgency(type);
        dispatchNotification(type, title, message, recipientUserIds, entityReferences, metadata, urgency);
    }

    /**
     * Dispatches a notification to multiple users with full control over urgency and metadata.
     *
     * <p>This is the most comprehensive dispatch method, providing full control over
     * notification urgency (affecting batching behavior) and using strongly-typed metadata
     * for type safety and clear data contracts.</p>
     *
     * @param type the notification type
     * @param title the notification title (max 255 characters)
     * @param message the notification message (max 1000 characters)
     * @param recipientUserIds the user IDs to notify (must not be empty)
     * @param entityReferences optional entity references for deep linking
     * @param metadata optional strongly-typed metadata providing additional context
     * @param urgency notification urgency level affecting batching and delivery timing
     *
     * @throws IllegalArgumentException if required parameters are invalid
     * @throws NotificationBatchingException if dispatch processing fails
     *
     * @example
     * <pre>
     * NotificationMetadata metadata = NotificationMetadata.builder()
     *     .actorName("John Doe")
     *     .targetTitle("Summer Campaign")
     *     .actionContext("mentioned you in a comment")
     *     .deepLinkPath("/app/posts/123#comment-456")
     *     .build();
     *
     * dispatcher.dispatchNotification(
     *     NotificationType.COMMENT_MENTION,
     *     "You were mentioned",
     *     "John Doe mentioned you in a comment on 'Summer Campaign'",
     *     List.of(userId),
     *     EntityReferences.comment(hubId, postId, commentId),
     *     metadata,
     *     NotificationUrgency.HIGH
     * );
     * </pre>
     */
    @Transactional
    public void dispatchNotification(@NotNull @Valid NotificationType type,
                                   @NotBlank @Size(max = MAX_TITLE_LENGTH) String title,
                                   @NotBlank @Size(max = MAX_MESSAGE_LENGTH) String message,
                                   @NotEmpty List<Long> recipientUserIds,
                                   NotificationStorageService.EntityReferences entityReferences,
                                   @Valid NotificationMetadata metadata,
                                   @NotNull @Valid NotificationUrgency urgency) {

        logger.debug(DISPATCH_START_MESSAGE, type, recipientUserIds.size());

        validateDispatchParameters(type, title, message, recipientUserIds, urgency);

        if (recipientUserIds.isEmpty()) {
            logger.debug(EMPTY_RECIPIENTS_MESSAGE, type);
            return;
        }

        NotificationService.NotificationDeliveryPlan deliveryPlan = createDeliveryPlan(type, recipientUserIds);

        processInAppNotifications(deliveryPlan, type, title, message, entityReferences, metadata);
        processEmailNotifications(deliveryPlan, type, title, message, entityReferences, metadata, urgency);

        logDispatchCompletion(type, urgency, deliveryPlan);
    }

    /**
     * Convenience method for simple notifications without entity references or metadata.
     *
     * <p>This method is useful for basic notifications that don't require additional
     * context or entity references. It uses default urgency based on notification type.</p>
     *
     * @param type the notification type (must not be null)
     * @param title the notification title (max 255 characters)
     * @param message the notification message (max 1000 characters)
     * @param recipientUserIds the user IDs to notify (must not be empty)
     *
     * @throws IllegalArgumentException if required parameters are invalid
     * @throws NotificationBatchingException if dispatch processing fails
     */
    @Transactional
    public void dispatchSimpleNotification(@NotNull @Valid NotificationType type,
                                         @NotBlank @Size(max = MAX_TITLE_LENGTH) String title,
                                         @NotBlank @Size(max = MAX_MESSAGE_LENGTH) String message,
                                         @NotEmpty List<Long> recipientUserIds) {
        dispatchNotification(type, title, message, recipientUserIds, null, NotificationMetadata.empty());
    }

    // ========================================
    // UNIFIED MIXED RECIPIENT METHODS
    // ========================================

    /**
     * Dispatches notifications to mixed recipients (internal and external users).
     *
     * <p>This is the primary method for the unified notification system, supporting both
     * internal users (with user_id) and external users (email-only). It provides full
     * feature parity including in-app notifications, email batching, and preferences.</p>
     *
     * @param type the notification type (must not be null)
     * @param title the notification title (max 255 characters)
     * @param message the notification message (max 1000 characters)
     * @param recipients the mixed recipient list (must not be empty)
     * @param entityReferences optional entity references for deep linking
     * @param metadata optional strongly-typed metadata providing additional context
     *
     * @throws IllegalArgumentException if required parameters are invalid
     * @throws NotificationBatchingException if dispatch processing fails
     */
    @Transactional
    public void dispatchMixedNotification(@NotNull @Valid NotificationType type,
                                        @NotBlank @Size(max = MAX_TITLE_LENGTH) String title,
                                        @NotBlank @Size(max = MAX_MESSAGE_LENGTH) String message,
                                        @NotEmpty List<NotificationRecipient> recipients,
                                        NotificationStorageService.EntityReferences entityReferences,
                                        @Valid NotificationMetadata metadata) {

        NotificationUrgency urgency = determineDefaultUrgency(type);
        dispatchMixedNotification(type, title, message, recipients, entityReferences, metadata, urgency);
    }

    /**
     * Dispatches notifications to mixed recipients with full urgency control.
     *
     * <p>This method provides complete control over notification dispatch for mixed
     * recipient types, including urgency levels and comprehensive validation.</p>
     *
     * @param type the notification type (must not be null)
     * @param title the notification title (max 255 characters)
     * @param message the notification message (max 1000 characters)
     * @param recipients the mixed recipient list (must not be empty)
     * @param entityReferences optional entity references for deep linking
     * @param metadata optional strongly-typed metadata providing additional context
     * @param urgency notification urgency level (must not be null)
     *
     * @throws IllegalArgumentException if required parameters are invalid
     * @throws NotificationBatchingException if dispatch processing fails
     */
    @Transactional
    public void dispatchMixedNotification(@NotNull @Valid NotificationType type,
                                        @NotBlank @Size(max = MAX_TITLE_LENGTH) String title,
                                        @NotBlank @Size(max = MAX_MESSAGE_LENGTH) String message,
                                        @NotEmpty List<NotificationRecipient> recipients,
                                        NotificationStorageService.EntityReferences entityReferences,
                                        @Valid NotificationMetadata metadata,
                                        @NotNull @Valid NotificationUrgency urgency) {

        validateMixedDispatchParameters(type, title, message, recipients, urgency);

        List<InternalUserRecipient> internalRecipients = NotificationRecipientUtils.filterInternalUsers(recipients);
        List<ExternalUserRecipient> externalRecipients = NotificationRecipientUtils.filterExternalUsers(recipients);

        logger.debug(MIXED_DISPATCH_START_MESSAGE, type, internalRecipients.size(), externalRecipients.size());

        processInternalUserNotifications(internalRecipients, type, title, message, entityReferences, metadata, urgency);
        processExternalUserNotifications(externalRecipients, type, title, message, entityReferences, metadata, urgency);

        logger.info(MIXED_DISPATCH_COMPLETE_MESSAGE, type, urgency, internalRecipients.size(), externalRecipients.size());
    }

    /**
     * Determines the default urgency level for a notification type.
     *
     * @param type the notification type
     * @return the default urgency level
     */
    private NotificationUrgency determineDefaultUrgency(@NotNull NotificationType type) {
        return NotificationUrgency.getDefaultForType(type);
    }

    /**
     * Validates dispatch parameters for consistency and business rules.
     *
     * @param type the notification type
     * @param title the notification title
     * @param message the notification message
     * @param recipientUserIds the recipient user IDs
     * @param urgency the notification urgency
     * @throws IllegalArgumentException if validation fails
     */
    private void validateDispatchParameters(@NotNull NotificationType type,
                                          @NotBlank String title,
                                          @NotBlank String message,
                                          @NotEmpty List<Long> recipientUserIds,
                                          @NotNull NotificationUrgency urgency) {

        if (title.length() > MAX_TITLE_LENGTH) {
            throw new IllegalArgumentException("Title exceeds maximum length of " + MAX_TITLE_LENGTH + " characters");
        }

        if (message.length() > MAX_MESSAGE_LENGTH) {
            throw new IllegalArgumentException("Message exceeds maximum length of " + MAX_MESSAGE_LENGTH + " characters");
        }

        if (recipientUserIds.stream().anyMatch(id -> id == null || id <= 0)) {
            throw new IllegalArgumentException("All recipient user IDs must be positive");
        }
    }

    /**
     * Creates a delivery plan based on user preferences.
     *
     * @param type the notification type
     * @param recipientUserIds the recipient user IDs
     * @return the delivery plan
     */
    private NotificationService.NotificationDeliveryPlan createDeliveryPlan(@NotNull NotificationType type,
                                                                           @NotEmpty List<Long> recipientUserIds) {
        return notificationService.processNotificationEvent(type, recipientUserIds);
    }

    /**
     * Processes in-app notifications if there are recipients.
     */
    private void processInAppNotifications(NotificationService.NotificationDeliveryPlan deliveryPlan,
                                         NotificationType type, String title, String message,
                                         NotificationStorageService.EntityReferences entityReferences,
                                         NotificationMetadata metadata) {
        if (deliveryPlan.hasInAppRecipients()) {
            handleInAppNotifications(type, title, message, deliveryPlan.getInAppRecipients(),
                                   entityReferences, metadata);
        }
    }

    /**
     * Processes email notifications if there are recipients.
     */
    private void processEmailNotifications(NotificationService.NotificationDeliveryPlan deliveryPlan,
                                         NotificationType type, String title, String message,
                                         NotificationStorageService.EntityReferences entityReferences,
                                         NotificationMetadata metadata, NotificationUrgency urgency) {
        if (deliveryPlan.hasEmailRecipients()) {
            handleEmailNotifications(type, title, message, deliveryPlan.getEmailRecipients(),
                                   entityReferences, metadata, urgency);
        }
    }

    /**
     * Logs the completion of notification dispatch.
     */
    private void logDispatchCompletion(NotificationType type, NotificationUrgency urgency,
                                     NotificationService.NotificationDeliveryPlan deliveryPlan) {
        logger.info(DISPATCH_COMPLETE_MESSAGE, type, urgency,
                   deliveryPlan.getInAppRecipients().size(), deliveryPlan.getEmailRecipients().size());
    }

    /**
     * Handles in-app notification delivery using strongly-typed metadata.
     */
    private void handleInAppNotifications(NotificationType type, String title, String message,
                                        Set<Long> recipientUserIds,
                                        NotificationStorageService.EntityReferences entityReferences,
                                        NotificationMetadata metadata) {

        logger.debug("Creating in-app notifications for {} users", recipientUserIds.size());

        notificationStorageService.createNotifications(type, title, message, recipientUserIds,
                                                      entityReferences, metadata);

        logger.info("Created {} in-app notifications for type: {}", recipientUserIds.size(), type);
    }

    /**
     * Handles email notification delivery with batching support using strongly-typed metadata.
     *
     * <p>This method processes email notifications through the batching system, which
     * can either queue notifications for later batch delivery or send them immediately
     * based on urgency level and batching configuration.</p>
     *
     * @param type the notification type
     * @param title the notification title
     * @param message the notification message
     * @param recipientUserIds the user IDs to send emails to
     * @param entityReferences optional entity references for deep linking
     * @param metadata strongly-typed metadata providing additional context
     * @param urgency notification urgency affecting batching behavior
     */
    private void handleEmailNotifications(NotificationType type, String title, String message,
                                        Set<Long> recipientUserIds,
                                        NotificationStorageService.EntityReferences entityReferences,
                                        NotificationMetadata metadata,
                                        NotificationUrgency urgency) {

        logger.debug("Processing email notifications for {} users with urgency {}", recipientUserIds.size(), urgency);

        Set<Long> validEmailRecipients = filterValidEmailRecipients(recipientUserIds);

        if (validEmailRecipients.isEmpty()) {
            logger.warn(NO_VALID_EMAIL_RECIPIENTS_MESSAGE, type);
            return;
        }

        Set<Long> immediateRecipients = queueOrSendImmediately(type, title, message, validEmailRecipients,
                                                              entityReferences, metadata, urgency);

        sendImmediateNotifications(type, title, message, immediateRecipients, entityReferences);
        logBatchingResults(validEmailRecipients, immediateRecipients, urgency);
    }

    /**
     * Queues notifications for batching or returns recipients for immediate sending.
     */
    private Set<Long> queueOrSendImmediately(NotificationType type, String title, String message,
                                           Set<Long> validEmailRecipients,
                                           NotificationStorageService.EntityReferences entityReferences,
                                           NotificationMetadata metadata, NotificationUrgency urgency) {
        return notificationBatchingService.queueNotificationsForBatching(
            type, title, message, validEmailRecipients, entityReferences, metadata, urgency);
    }

    /**
     * Sends immediate email notifications if any recipients are provided.
     */
    private void sendImmediateNotifications(NotificationType type, String title, String message,
                                          Set<Long> immediateRecipients,
                                          NotificationStorageService.EntityReferences entityReferences) {
        if (!immediateRecipients.isEmpty()) {
            emailNotificationService.sendEmailNotifications(type, title, message,
                                                           immediateRecipients, entityReferences);
            logger.info("Sent immediate email notifications to {} recipients", immediateRecipients.size());
        }
    }

    /**
     * Logs the results of batching operations.
     */
    private void logBatchingResults(Set<Long> validEmailRecipients, Set<Long> immediateRecipients,
                                  NotificationUrgency urgency) {
        int batchedCount = validEmailRecipients.size() - immediateRecipients.size();
        if (batchedCount > 0) {
            logger.info("Queued {} email notifications for batching with urgency {}", batchedCount, urgency);
        }
    }



    /**
     * Filters recipient user IDs to ensure they have valid email addresses.
     *
     * <p>This method performs bulk loading to avoid N+1 query issues and validates
     * that users have non-null, non-empty email addresses for email delivery.</p>
     *
     * @param recipientUserIds the user IDs to filter (must not be null)
     * @return set of user IDs that have valid email addresses
     * @throws IllegalArgumentException if recipientUserIds is null
     */
    private Set<Long> filterValidEmailRecipients(@NotNull Set<Long> recipientUserIds) {
        if (recipientUserIds.isEmpty()) {
            return Set.of();
        }

        // Bulk load users to avoid N+1 queries
        List<User> users = userRepository.findByIds(recipientUserIds.stream().toList());

        return users.stream()
                .filter(this::hasValidEmail)
                .map(User::getId)
                .collect(Collectors.toSet());
    }

    /**
     * Checks if a user has a valid email address.
     *
     * @param user the user to check
     * @return true if the user has a valid email address
     */
    private boolean hasValidEmail(@NotNull User user) {
        return user.getEmail() != null && !user.getEmail().trim().isEmpty();
    }

    // ========================================
    // MIXED RECIPIENT PROCESSING METHODS
    // ========================================

    /**
     * Validates mixed dispatch parameters for consistency and business rules.
     */
    private void validateMixedDispatchParameters(@NotNull NotificationType type,
                                               @NotBlank String title,
                                               @NotBlank String message,
                                               @NotEmpty List<NotificationRecipient> recipients,
                                               @NotNull NotificationUrgency urgency) {

        validateDispatchParameters(type, title, message, List.of(), urgency); // Basic validation

        if (recipients.isEmpty()) {
            throw new IllegalArgumentException("Recipients list cannot be empty");
        }

        // Validate all recipients
        NotificationRecipientUtils.validateRecipients(recipients);
    }

    /**
     * Processes notifications for internal users through the existing system.
     */
    private void processInternalUserNotifications(List<InternalUserRecipient> internalRecipients,
                                                NotificationType type, String title, String message,
                                                NotificationStorageService.EntityReferences entityReferences,
                                                NotificationMetadata metadata, NotificationUrgency urgency) {

        if (internalRecipients.isEmpty()) {
            return;
        }

        List<Long> userIds = internalRecipients.stream()
                                              .map(InternalUserRecipient::getUserId)
                                              .collect(Collectors.toList());

        // Use existing internal user processing
        dispatchNotification(type, title, message, userIds, entityReferences, metadata, urgency);
    }

    /**
     * Processes notifications for external users with feature parity.
     */
    private void processExternalUserNotifications(List<ExternalUserRecipient> externalRecipients,
                                                NotificationType type, String title, String message,
                                                NotificationStorageService.EntityReferences entityReferences,
                                                NotificationMetadata metadata, NotificationUrgency urgency) {

        if (externalRecipients.isEmpty()) {
            return;
        }

        logger.debug("Processing notifications for {} external users", externalRecipients.size());

        // Create delivery plan for external users based on preferences
        ExternalNotificationDeliveryPlan deliveryPlan = createExternalDeliveryPlan(type, externalRecipients);

        // Process email notifications (external users only get email notifications)
        processExternalEmailNotifications(deliveryPlan, type, title, message, entityReferences, metadata, urgency);

        logger.info("Processed notifications for {} external users", externalRecipients.size());
    }

    /**
     * Creates a delivery plan for external users based on preferences.
     */
    private ExternalNotificationDeliveryPlan createExternalDeliveryPlan(NotificationType type,
                                                                       List<ExternalUserRecipient> recipients) {
        ExternalNotificationDeliveryPlan deliveryPlan = new ExternalNotificationDeliveryPlan();

        for (ExternalUserRecipient recipient : recipients) {
            // Check email preferences for external users
            Boolean emailEnabled = notificationPreferenceService.isExternalNotificationEnabled(
                recipient.getEmail(), type, NotificationChannel.EMAIL);

            if (emailEnabled != null && emailEnabled) {
                deliveryPlan.addEmailRecipient(recipient);
            }
        }

        return deliveryPlan;
    }

    /**
     * Processes email notifications for external users.
     */
    private void processExternalEmailNotifications(ExternalNotificationDeliveryPlan deliveryPlan,
                                                 NotificationType type, String title, String message,
                                                 NotificationStorageService.EntityReferences entityReferences,
                                                 NotificationMetadata metadata, NotificationUrgency urgency) {

        if (!deliveryPlan.hasEmailRecipients()) {
            return;
        }

        List<ExternalUserRecipient> emailRecipients = deliveryPlan.getEmailRecipients();

        if (urgency.shouldBypassBatching()) {
            // Send immediately
            externalEmailNotificationService.sendImmediateEmails(type, title, message, emailRecipients, entityReferences);
        } else {
            // Queue for batching
            notificationBatchingService.queueExternalNotificationsForBatching(
                type, title, message, emailRecipients, entityReferences, metadata, urgency);
        }
    }

    /**
     * Data class for external user delivery planning.
     */
    private static class ExternalNotificationDeliveryPlan {
        private final List<ExternalUserRecipient> emailRecipients = new java.util.ArrayList<>();

        public void addEmailRecipient(ExternalUserRecipient recipient) {
            emailRecipients.add(recipient);
        }

        public List<ExternalUserRecipient> getEmailRecipients() {
            return emailRecipients;
        }

        public boolean hasEmailRecipients() {
            return !emailRecipients.isEmpty();
        }
    }


}
