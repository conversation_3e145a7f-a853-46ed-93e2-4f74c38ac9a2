package com.collabhub.be.modules.notifications.service;

import com.collabhub.be.modules.auth.service.EmailService;
import com.collabhub.be.modules.notifications.dto.NotificationType;
import com.collabhub.be.modules.notifications.dto.NotificationUrgency;
import com.collabhub.be.modules.auth.repository.UserRepository;
import org.jooq.generated.tables.pojos.NotificationBatchQueue;
import org.jooq.generated.tables.pojos.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Service for sending batched email notifications.
 * Consolidates multiple notifications into a single email to reduce email spam.
 */
@Service
public class BatchedEmailNotificationService {

    private static final Logger logger = LoggerFactory.getLogger(BatchedEmailNotificationService.class);
    
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("MMM dd, yyyy");

    private final EmailService emailService;
    private final UserRepository userRepository;
    private final TemplateEngine templateEngine;
    private final NotificationPreferenceService notificationPreferenceService;

    public BatchedEmailNotificationService(EmailService emailService,
                                         UserRepository userRepository,
                                         TemplateEngine templateEngine,
                                         NotificationPreferenceService notificationPreferenceService) {
        this.emailService = emailService;
        this.userRepository = userRepository;
        this.templateEngine = templateEngine;
        this.notificationPreferenceService = notificationPreferenceService;
    }

    /**
     * Sends a batched email containing multiple notifications for a user.
     *
     * @param userId the user ID to send the email to
     * @param notifications list of notifications to include in the batch
     * @return true if email was sent successfully, false otherwise
     */
    public boolean sendBatchedEmail(Long userId, List<NotificationBatchQueue> notifications) {
        if (notifications.isEmpty()) {
            logger.debug("No notifications to send for user {}", userId);
            return true;
        }

        logger.debug("Sending batched email to user {} with {} notifications", userId, notifications.size());

        try {
            // Get user information
            User user = userRepository.findById(userId);
            if (user == null || user.getEmail() == null || user.getEmail().trim().isEmpty()) {
                logger.warn("User {} not found or has no email address", userId);
                return false;
            }

            // Check if user wants to receive email notifications
            if (!shouldSendBatchedEmail(user.getEmail(), notifications)) {
                logger.debug("User {} has disabled email notifications for these types", userId);
                return true; // Consider this successful (user preference)
            }

            // Group notifications by type for better organization
            Map<NotificationType, List<NotificationBatchQueue>> notificationsByType = 
                notifications.stream()
                    .collect(Collectors.groupingBy(n -> 
                        NotificationType.valueOf(n.getNotificationType().name())));

            // Generate email content
            String subject = generateBatchedEmailSubject(notifications.size(), notificationsByType);
            String htmlContent = generateBatchedEmailContent(user, notifications, notificationsByType);

            // Send the email
            boolean sent = emailService.sendHtmlEmail(user.getEmail(), subject, htmlContent);
            
            if (sent) {
                logger.info("Successfully sent batched email to user {} with {} notifications", 
                           userId, notifications.size());
            } else {
                logger.warn("Failed to send batched email to user {}", userId);
            }

            return sent;

        } catch (Exception e) {
            logger.error("Exception while sending batched email to user {}", userId, e);
            return false;
        }
    }

    /**
     * Checks if a batched email should be sent based on user preferences.
     */
    private boolean shouldSendBatchedEmail(String email, List<NotificationBatchQueue> notifications) {
        // Check if user has any email notifications enabled for the types in this batch
        for (NotificationBatchQueue notification : notifications) {
            NotificationType type = NotificationType.valueOf(notification.getNotificationType().name());
            if (notificationPreferenceService.shouldSendEmailNotification(email, type)) {
                return true; // At least one notification type is enabled
            }
        }
        return false;
    }

    /**
     * Generates the subject line for a batched email.
     */
    private String generateBatchedEmailSubject(int notificationCount, 
                                             Map<NotificationType, List<NotificationBatchQueue>> notificationsByType) {
        if (notificationCount == 1) {
            // Single notification - use specific subject
            NotificationType type = notificationsByType.keySet().iterator().next();
            NotificationBatchQueue notification = notificationsByType.get(type).get(0);
            return notification.getTitle();
        }

        // Multiple notifications - use summary subject
        if (notificationsByType.size() == 1) {
            // All notifications are of the same type
            NotificationType type = notificationsByType.keySet().iterator().next();
            return String.format("%d new %s notifications", notificationCount, 
                               getTypeDisplayName(type).toLowerCase());
        }

        // Mixed notification types
        return String.format("%d new notifications from Collaboration Hub", notificationCount);
    }

    /**
     * Generates the HTML content for a batched email using Thymeleaf template.
     */
    private String generateBatchedEmailContent(User user, List<NotificationBatchQueue> notifications,
                                             Map<NotificationType, List<NotificationBatchQueue>> notificationsByType) {
        
        Context context = new Context();
        
        // User information
        context.setVariable("userName", user.getDisplayName() != null ? user.getDisplayName() : user.getEmail());
        context.setVariable("userEmail", user.getEmail());
        
        // Notification summary
        context.setVariable("totalNotifications", notifications.size());
        context.setVariable("notificationTypes", notificationsByType.size());
        
        // Group notifications for display
        List<NotificationGroup> notificationGroups = notificationsByType.entrySet().stream()
                .map(entry -> new NotificationGroup(
                    entry.getKey(),
                    getTypeDisplayName(entry.getKey()),
                    entry.getValue().stream()
                        .map(this::convertToDisplayNotification)
                        .collect(Collectors.toList())
                ))
                .collect(Collectors.toList());
        
        context.setVariable("notificationGroups", notificationGroups);
        
        // Timing information
        context.setVariable("batchTime", notifications.get(0).getCreatedAt().format(TIME_FORMATTER));
        context.setVariable("batchDate", notifications.get(0).getCreatedAt().format(DATE_FORMATTER));
        
        // Process the template
        return templateEngine.process("email/batched-notifications", context);
    }

    /**
     * Converts a batch queue notification to a display-friendly format.
     */
    private DisplayNotification convertToDisplayNotification(NotificationBatchQueue notification) {
        return new DisplayNotification(
            notification.getTitle(),
            notification.getMessage(),
            notification.getCreatedAt().format(TIME_FORMATTER),
            NotificationUrgency.fromJooqEnum(notification.getUrgency()),
            generateNotificationUrl(notification)
        );
    }

    /**
     * Generates a deep link URL for a notification.
     */
    private String generateNotificationUrl(NotificationBatchQueue notification) {
        // This would generate URLs based on the entity references
        // For now, return a generic dashboard URL
        return "/app/notifications";
    }

    /**
     * Gets a user-friendly display name for a notification type.
     */
    private String getTypeDisplayName(NotificationType type) {
        return switch (type) {
            case INVITE_TO_HUB -> "Hub Invitations";
            case ASSIGNED_AS_REVIEWER -> "Review Assignments";
            case POST_REVIEWED -> "Post Reviews";
            case COMMENT_ADDED -> "Comments";
            case COMMENT_MENTION -> "Comment Mentions";
            case CHAT_MENTION -> "Chat Mentions";
            case CHAT_ADDED -> "Chat Additions";
            case BRIEF_CREATED -> "New Briefs";
            case BRIEF_UPDATED -> "Brief Updates";
            case BRIEF_ASSIGNED -> "Brief Assignments";
        };
    }

    /**
     * Data class for grouping notifications by type in the email template.
     */
    public static class NotificationGroup {
        private final NotificationType type;
        private final String displayName;
        private final List<DisplayNotification> notifications;

        public NotificationGroup(NotificationType type, String displayName, List<DisplayNotification> notifications) {
            this.type = type;
            this.displayName = displayName;
            this.notifications = notifications;
        }

        // Getters
        public NotificationType getType() { return type; }
        public String getDisplayName() { return displayName; }
        public List<DisplayNotification> getNotifications() { return notifications; }
        public int getCount() { return notifications.size(); }
    }

    /**
     * Data class for individual notifications in the email template.
     */
    public static class DisplayNotification {
        private final String title;
        private final String message;
        private final String time;
        private final NotificationUrgency urgency;
        private final String url;

        public DisplayNotification(String title, String message, String time, 
                                 NotificationUrgency urgency, String url) {
            this.title = title;
            this.message = message;
            this.time = time;
            this.urgency = urgency;
            this.url = url;
        }

        // Getters
        public String getTitle() { return title; }
        public String getMessage() { return message; }
        public String getTime() { return time; }
        public NotificationUrgency getUrgency() { return urgency; }
        public String getUrl() { return url; }
        public boolean isHighPriority() { return urgency == NotificationUrgency.HIGH || urgency == NotificationUrgency.URGENT; }
    }
}
