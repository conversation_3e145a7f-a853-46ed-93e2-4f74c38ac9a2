package com.collabhub.be.modules.notifications.service;

import com.collabhub.be.modules.notifications.dto.NotificationType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * Service for handling notification translations and internationalization.
 * Provides localized notification titles, messages, and email templates.
 * 
 * This service is designed to be production-ready with proper error handling,
 * fallbacks, and extensibility for future languages.
 */
@Service
public class NotificationTranslationService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationTranslationService.class);
    
    // Default locale for fallback
    private static final Locale DEFAULT_LOCALE = Locale.ENGLISH;
    
    // Translation keys
    private static final String TITLE_KEY_PREFIX = "notification.title.";
    private static final String MESSAGE_KEY_PREFIX = "notification.message.";
    private static final String EMAIL_SUBJECT_KEY_PREFIX = "notification.email.subject.";
    private static final String EMAIL_BODY_KEY_PREFIX = "notification.email.body.";
    
    // Translation storage - in production, this could be loaded from database or external service
    private final Map<String, Map<String, String>> translations = new HashMap<>();
    
    public NotificationTranslationService() {
        initializeTranslations();
    }
    
    /**
     * Gets localized notification title.
     */
    public String getNotificationTitle(NotificationType type, Locale locale) {
        return getTranslation(TITLE_KEY_PREFIX + type.name().toLowerCase(), locale, getDefaultTitle(type));
    }
    
    /**
     * Gets localized notification message with parameter substitution.
     */
    public String getNotificationMessage(NotificationType type, Locale locale, Map<String, Object> parameters) {
        String template = getTranslation(MESSAGE_KEY_PREFIX + type.name().toLowerCase(), locale, getDefaultMessage(type));
        return substituteParameters(template, parameters);
    }
    
    /**
     * Gets localized email subject.
     */
    public String getEmailSubject(NotificationType type, Locale locale, Map<String, Object> parameters) {
        String template = getTranslation(EMAIL_SUBJECT_KEY_PREFIX + type.name().toLowerCase(), locale, getDefaultEmailSubject(type));
        return substituteParameters(template, parameters);
    }
    
    /**
     * Gets localized email body.
     */
    public String getEmailBody(NotificationType type, Locale locale, Map<String, Object> parameters) {
        String template = getTranslation(EMAIL_BODY_KEY_PREFIX + type.name().toLowerCase(), locale, getDefaultEmailBody(type));
        return substituteParameters(template, parameters);
    }
    
    /**
     * Gets translation for a specific key and locale with fallback.
     */
    private String getTranslation(String key, Locale locale, String defaultValue) {
        try {
            String localeKey = locale != null ? locale.getLanguage() : DEFAULT_LOCALE.getLanguage();
            
            Map<String, String> localeTranslations = translations.get(localeKey);
            if (localeTranslations != null && localeTranslations.containsKey(key)) {
                return localeTranslations.get(key);
            }
            
            // Fallback to default locale
            if (!DEFAULT_LOCALE.getLanguage().equals(localeKey)) {
                Map<String, String> defaultTranslations = translations.get(DEFAULT_LOCALE.getLanguage());
                if (defaultTranslations != null && defaultTranslations.containsKey(key)) {
                    return defaultTranslations.get(key);
                }
            }
            
            // Final fallback to provided default
            return defaultValue;
            
        } catch (Exception e) {
            logger.warn("Failed to get translation for key '{}' and locale '{}': {}", key, locale, e.getMessage());
            return defaultValue;
        }
    }
    
    /**
     * Substitutes parameters in template strings.
     * Supports {paramName} syntax.
     */
    private String substituteParameters(String template, Map<String, Object> parameters) {
        if (template == null || parameters == null || parameters.isEmpty()) {
            return template;
        }
        
        String result = template;
        for (Map.Entry<String, Object> entry : parameters.entrySet()) {
            String placeholder = "{" + entry.getKey() + "}";
            String value = entry.getValue() != null ? entry.getValue().toString() : "";
            result = result.replace(placeholder, value);
        }
        
        return result;
    }
    
    /**
     * Initializes default English translations.
     * In production, this could load from database, properties files, or external service.
     */
    private void initializeTranslations() {
        Map<String, String> englishTranslations = new HashMap<>();
        
        // Notification titles
        englishTranslations.put("notification.title.invite_to_hub", "Invitation to Collaboration Hub");
        englishTranslations.put("notification.title.assigned_as_reviewer", "Assigned as Reviewer");
        englishTranslations.put("notification.title.post_reviewed", "Post Reviewed");
        englishTranslations.put("notification.title.comment_added", "New Comment");
        englishTranslations.put("notification.title.comment_mention", "Mentioned in Comment");
        englishTranslations.put("notification.title.chat_mention", "Mentioned in Chat");
        englishTranslations.put("notification.title.chat_added", "Added to Chat");
        
        // Notification messages
        englishTranslations.put("notification.message.invite_to_hub", "{inviterName} invited you to join '{hubName}'");
        englishTranslations.put("notification.message.assigned_as_reviewer", "{assignerName} assigned you to review '{postTitle}'");
        englishTranslations.put("notification.message.post_reviewed", "{reviewerName} {reviewStatus} your post '{postTitle}'");
        englishTranslations.put("notification.message.comment_added", "{commenterName} commented on '{postTitle}': {commentPreview}");
        englishTranslations.put("notification.message.comment_mention", "{commenterName} mentioned you in a comment on '{postTitle}': {commentPreview}");
        englishTranslations.put("notification.message.chat_mention", "{senderName} mentioned you in '{channelName}': {messagePreview}");
        englishTranslations.put("notification.message.chat_added", "{adderName} added you to the chat '{channelName}'");
        
        // Email subjects
        englishTranslations.put("notification.email.subject.invite_to_hub", "You're invited to join {hubName}");
        englishTranslations.put("notification.email.subject.assigned_as_reviewer", "Review Assignment: {postTitle}");
        englishTranslations.put("notification.email.subject.post_reviewed", "Your post has been reviewed");
        englishTranslations.put("notification.email.subject.comment_added", "New comment on {postTitle}");
        englishTranslations.put("notification.email.subject.comment_mention", "You were mentioned in a comment");
        englishTranslations.put("notification.email.subject.chat_mention", "You were mentioned in {channelName}");
        englishTranslations.put("notification.email.subject.chat_added", "Added to chat: {channelName}");
        
        // Email bodies
        englishTranslations.put("notification.email.body.invite_to_hub", 
            "Hi {recipientName},\n\n{inviterName} has invited you to join the collaboration hub '{hubName}'.\n\nBest regards,\nCollaboration Hub Team");
        englishTranslations.put("notification.email.body.assigned_as_reviewer", 
            "Hi {recipientName},\n\n{assignerName} has assigned you to review the post '{postTitle}'.\n\nPlease log in to review the content.\n\nBest regards,\nCollaboration Hub Team");
        englishTranslations.put("notification.email.body.post_reviewed", 
            "Hi {recipientName},\n\n{reviewerName} has {reviewStatus} your post '{postTitle}'.\n\nPlease check the review details in your dashboard.\n\nBest regards,\nCollaboration Hub Team");
        englishTranslations.put("notification.email.body.comment_added", 
            "Hi {recipientName},\n\n{commenterName} commented on your post '{postTitle}':\n\n\"{commentPreview}\"\n\nBest regards,\nCollaboration Hub Team");
        englishTranslations.put("notification.email.body.comment_mention", 
            "Hi {recipientName},\n\n{commenterName} mentioned you in a comment on '{postTitle}':\n\n\"{commentPreview}\"\n\nBest regards,\nCollaboration Hub Team");
        englishTranslations.put("notification.email.body.chat_mention", 
            "Hi {recipientName},\n\n{senderName} mentioned you in the chat '{channelName}':\n\n\"{messagePreview}\"\n\nBest regards,\nCollaboration Hub Team");
        englishTranslations.put("notification.email.body.chat_added", 
            "Hi {recipientName},\n\n{adderName} added you to the chat '{channelName}'.\n\nYou can now participate in the conversation.\n\nBest regards,\nCollaboration Hub Team");
        
        translations.put("en", englishTranslations);
        
        logger.info("Initialized notification translations for {} locales", translations.size());
    }
    
    // Default fallback methods
    private String getDefaultTitle(NotificationType type) {
        return "Notification: " + type.name();
    }
    
    private String getDefaultMessage(NotificationType type) {
        return "You have a new " + type.name().toLowerCase().replace("_", " ") + " notification.";
    }
    
    private String getDefaultEmailSubject(NotificationType type) {
        return "Collaboration Hub: " + type.name().replace("_", " ");
    }
    
    private String getDefaultEmailBody(NotificationType type) {
        return "You have received a notification from Collaboration Hub.\n\nBest regards,\nCollaboration Hub Team";
    }
}
